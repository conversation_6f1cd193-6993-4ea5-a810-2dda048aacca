hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@esbuild/win32-x64@0.25.8':
    '@esbuild/win32-x64': private
  '@floating-ui/core@1.7.2':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.2':
    '@floating-ui/dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@rolldown/pluginutils@1.0.0-beta.19':
    '@rolldown/pluginutils': private
  '@rollup/rollup-win32-x64-msvc@4.45.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@sxzz/popperjs-es@2.11.7':
    '@popperjs/core': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': private
  '@types/lodash@4.17.20':
    '@types/lodash': private
  '@types/web-bluetooth@0.0.16':
    '@types/web-bluetooth': private
  '@volar/language-core@2.4.15':
    '@volar/language-core': private
  '@volar/source-map@2.4.15':
    '@volar/source-map': private
  '@volar/typescript@2.4.15':
    '@volar/typescript': private
  '@vue/compiler-core@3.5.18':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.18':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.18':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.18':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@7.7.7':
    '@vue/devtools-api': private
  '@vue/devtools-kit@7.7.7':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.7':
    '@vue/devtools-shared': private
  '@vue/language-core@2.2.12(typescript@5.8.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.18':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.18':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.18':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.18(vue@3.5.18(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.18':
    '@vue/shared': private
  '@vueuse/core@9.13.0(vue@3.5.18(typescript@5.8.3))':
    '@vueuse/core': private
  '@vueuse/metadata@9.13.0':
    '@vueuse/metadata': private
  '@vueuse/shared@9.13.0(vue@3.5.18(typescript@5.8.3))':
    '@vueuse/shared': private
  alien-signals@1.0.13:
    alien-signals: private
  async-validator@4.2.5:
    async-validator: private
  balanced-match@1.0.2:
    balanced-match: private
  birpc@2.5.0:
    birpc: private
  brace-expansion@2.0.2:
    brace-expansion: private
  copy-anything@3.0.5:
    copy-anything: private
  csstype@3.1.3:
    csstype: private
  de-indent@1.0.2:
    de-indent: private
  entities@4.5.0:
    entities: private
  esbuild@0.25.8:
    esbuild: private
  escape-html@1.0.3:
    escape-html: private
  estree-walker@2.0.2:
    estree-walker: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  he@1.2.0:
    he: private
  hookable@5.5.3:
    hookable: private
  is-what@4.1.16:
    is-what: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    lodash-unified: private
  lodash@4.17.21:
    lodash: private
  magic-string@0.30.17:
    magic-string: private
  memoize-one@6.0.0:
    memoize-one: private
  minimatch@9.0.5:
    minimatch: private
  mitt@3.0.1:
    mitt: private
  muggle-string@0.4.1:
    muggle-string: private
  nanoid@3.3.11:
    nanoid: private
  normalize-wheel-es@1.2.0:
    normalize-wheel-es: private
  path-browserify@1.0.1:
    path-browserify: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  rfdc@1.4.1:
    rfdc: private
  rollup@4.45.1:
    rollup: private
  source-map-js@1.2.1:
    source-map-js: private
  speakingurl@14.0.1:
    speakingurl: private
  superjson@2.2.2:
    superjson: private
  tinyglobby@0.2.14:
    tinyglobby: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-demi@0.14.10(vue@3.5.18(typescript@5.8.3)):
    vue-demi: private
ignoredBuilds:
  - vue-demi
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.13.1
pendingBuilds: []
prunedAt: Fri, 25 Jul 2025 03:27:22 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@rollup/rollup-android-arm-eabi@4.45.1'
  - '@rollup/rollup-android-arm64@4.45.1'
  - '@rollup/rollup-darwin-arm64@4.45.1'
  - '@rollup/rollup-darwin-x64@4.45.1'
  - '@rollup/rollup-freebsd-arm64@4.45.1'
  - '@rollup/rollup-freebsd-x64@4.45.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.45.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.45.1'
  - '@rollup/rollup-linux-arm64-gnu@4.45.1'
  - '@rollup/rollup-linux-arm64-musl@4.45.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.45.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-musl@4.45.1'
  - '@rollup/rollup-linux-s390x-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-musl@4.45.1'
  - '@rollup/rollup-win32-arm64-msvc@4.45.1'
  - '@rollup/rollup-win32-ia32-msvc@4.45.1'
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\桌面\AI编程\小游戏\Echo Chamber\node_modules\.pnpm
virtualStoreDirMaxLength: 60
