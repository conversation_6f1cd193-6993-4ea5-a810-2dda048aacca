# Security Policy

## Supported Versions

| Version | Supported          | Branch
| ------- | ------------------ | --------
| 4.x     | :white_check_mark: | master
| 3.x     | :white_check_mark: | master-3
| 2.0.x   | :x: | master-2
| 1.5.x   | :x: | master-1
| < 1.5.1 | :x:                |

## Reporting a Vulnerability

To report a security vulnerability in Dexie.js, please send an <NAME_EMAIL> describing the vulnerability and how to reproduce it.

If we find an issue to be regarded as a security vulnerability, we will patch and release a new version in all the supported versions as soon as possible.
Keep in mind though that this is an uncommercial open source project which means that sometimes you might have to be the one that
*fixes* the issue and not just report it.

## Fixing a Vulnerability

Fix the issue in the corresponding branch for the major version according to the table above where it applies and
create pull requests. Make sure that you have the words "security" or "vulnerability" in the title of the Pull Request
in order to get the correct attention for it to be merged and released as soon as possible.

