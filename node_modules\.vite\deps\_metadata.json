{"hash": "c00d3d31", "configHash": "83925112", "lockfileHash": "3f3e52cf", "browserHash": "a82ff0da", "optimized": {"@element-plus/icons-vue": {"src": "../../.pnpm/@element-plus+icons-vue@2.3.1_vue@3.5.18_typescript@5.8.3_/node_modules/@element-plus/icons-vue/dist/index.js", "file": "@element-plus_icons-vue.js", "fileHash": "8f730a72", "needsInterop": false}, "element-plus": {"src": "../../.pnpm/element-plus@2.10.4_vue@3.5.18_typescript@5.8.3_/node_modules/element-plus/es/index.mjs", "file": "element-plus.js", "fileHash": "4e2c910b", "needsInterop": false}, "pinia": {"src": "../../.pnpm/pinia@3.0.3_typescript@5.8.3_vue@3.5.18_typescript@5.8.3_/node_modules/pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "8c3cadf6", "needsInterop": false}, "vue": {"src": "../../.pnpm/vue@3.5.18_typescript@5.8.3/node_modules/vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "c41e1d81", "needsInterop": false}, "vue-router": {"src": "../../.pnpm/vue-router@4.5.1_vue@3.5.18_typescript@5.8.3_/node_modules/vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "1aacd1f8", "needsInterop": false}}, "chunks": {"chunk-PDCJM57Y": {"file": "chunk-PDCJM57Y.js"}, "chunk-IW3WGNG3": {"file": "chunk-IW3WGNG3.js"}}}